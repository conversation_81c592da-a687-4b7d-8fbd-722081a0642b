Metadata-Version: 2.1
Name: cx_Oracle
Version: 8.3.0
Summary: Python interface to Oracle
Home-page: https://oracle.github.io/python-cx_Oracle
Author: "<PERSON>",
Author-email: "<EMAIL>",
License: BSD License
Project-URL: Installation, https://cx-oracle.readthedocs.io/en/latest/user_guide/installation.html
Project-URL: Samples, https://github.com/oracle/python-cx_Oracle/tree/main/samples
Project-URL: Documentation, http://cx-oracle.readthedocs.io
Project-URL: Release Notes, https://cx-oracle.readthedocs.io/en/latest/release_notes.html#releasenotes
Project-URL: Issues, https://github.com/oracle/python-cx_Oracle/issues
Project-URL: Source, https://github.com/oracle/python-cx_Oracle
Keywords: Oracle,database
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Database
Description-Content-Type: text/markdown
License-File: LICENSE.txt

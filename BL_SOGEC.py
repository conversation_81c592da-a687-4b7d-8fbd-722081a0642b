# -*-coding:utf-8 -*
# <PERSON> VOLFF

import cx_Oracle  # pip install cx-Oracle
import keyring  # pip install keyring # https://pypi.org/project/keyring/
import requests  # pip install requests

USER_CCE3 = "cce3"
PASSWORD_CCE3 = keyring.get_password(
    "oracle", "cce3"
)  # en ligne de commande windows pour indiquer le mot de passe : keyring set oracle cce3

# https://www.oracle.com/fr/database/technologies/instant-client/winx64-64-downloads.html
# https://download.oracle.com/otn_software/nt/instantclient/213000/instantclient-basic-windows.x64-********.0.zip
# https://download.oracle.com/otn_software/nt/instantclient/2113000/instantclient-basic-windows.x64-*********.0dbru.zip
cx_Oracle.init_oracle_client(lib_dir=r"C:\instantclient_21_13")
connection = cx_Oracle.connect(
    user=USER_CCE3, password=PASSWORD_CCE3, dsn="//*************:1549/CNCE"
)
cursor = connection.cursor()

# BL SOGEC on récupère DLO_CODE
cursor.execute(
    """ SELECT DT.DTP_LABEL, DL.ORD_ID, DL.DLO_CODE
    FROM CTB_DELIVERY DL INNER JOIN CTB_DELIVERYTYPE DT
    ON DT.DTP_ID = DL.DTP_ID
   WHERE DL.DLO_CODE IN(
   SELECT D.DLO_CODE
   FROM CTB_DELIVERY D 
   LEFT JOIN CTB_ORDLDELIVERY ORDL ON ORDL.DLO_CODE = D.DLO_CODE
   LEFT JOIN CTB_ORDLINE L ON (L.ORD_ID = ORDL.ORD_ID AND L.ORL_ID = ORDL.ORL_ID)
   LEFT JOIN CTB_PRODUCT P ON P.PRD_CODE = L.PRD_CODE
   LEFT JOIN CTB_IVTRTYPE IT ON IT.ITY_ID = D.ITY_ID
   LEFT JOIN CTB_IVTRTYPE ITNC ON ITNC.ITY_ID = D.ITY_ID_NC
   WHERE D.STA_CODE IN ('V800', 'M200')
    AND (P.PPC_ID IS NULL OR P.PPC_ID <> 10)
    AND (NVL(IT.ITY_SUBMITYN, ITNC.ITY_SUBMITYN) = 'Y')
    AND D.DLO_STATUSDT < SYSDATE - 15 / 1440
   )
  """
)

bl_ok = {}

for dpt_label, ord_id, dlo_code in cursor:
    # print(dpt_label, ord_id, dlo_code)
    if "Bon de livraison" in dpt_label:
        url = f"https://documents.kalidea.com/pages/BonLivraison.aspx?OrdId={ord_id}&DloCode={dlo_code}"
        r = requests.get(url)
        if "Bon de livraison" not in r.text:
            print(f"ERREUR : {url} ")
        else:
            bl_ok[ord_id] = [dlo_code, url]
    elif "Bon de réception" in dpt_label:
        url = (
            f"https://documents.kalidea.com/pages/BonReception.aspx?DloCode={dlo_code}"
        )
        r = requests.get(url)
        if "Bon de réception" not in r.text:
            print(f"ERREUR : {url} ")
    elif "Bon de mouvement" in dpt_label:
        url = (
            f"https://documents.kalidea.com/pages/BonMouvement.aspx?DloCode={dlo_code}"
        )
        r = requests.get(url)
        if "Bon de mouvement" not in r.text:
            print(f"ERREUR : {url} ")

if bl_ok:
    print("Mail à envoyer : <EMAIL>\n")
    print("Objet : Erreur BL Sogec\n")
    if len(bl_ok) > 1:
        print("Bonjour,\n\nNous constatons une alerte sur ces commandes :\n")
    else:
        print("Bonjour,\n\nNous constatons une alerte sur cette commande :\n")
    for key, value in bl_ok.items():
        print(f"OrdId : {key}")
        print(f"DloCode : {value[0]}")
        print(f"{url}\n")
    print(
        "Le PDF est bien disponible et pas de problème de génération sur le serveur.\n"
    )
    print("Pouvez-vous intervenir ?")

cursor.close()
connection.close()
